"use client";

import React, { useEffect, useRef, useState } from "react";
import { Icon } from "@iconify/react";
import useDataFetch from "@/hooks/useDataFetch";
import Image from "next/image";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "@/components/LanguageSwicher";
import { useRouter } from "next/navigation";
import { logout } from "@/store/features/authSlice";
import {
  setHistoryActiveCategory,
  setMainProfileActiveTab,
} from "@/store/features/profileSlice";
import NavSearch from "./NavSearch";
import {
  fetchAuthors,
  fetchCategories,
  fetchPublishers,
} from "@/store/features/commonApiSlice";
import { useAppDispatch, useAppSelector } from "@/hooks/reduxHooks";
import { setShowModal } from "@/store/features/commonSice";

const Navbar = () => {
  const { t } = useTranslation();
  const { categories, authors, publishers } = useAppSelector(
    (state) => state.commonApi
  );
  const { isAuth, user } = useAppSelector((state) => state.auth);
  const { cart } = useAppSelector((state) => state.cart);
  const dispatch = useAppDispatch();
  const router = useRouter();

  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isCategoryInnerMenuOpen, setIsCategoryInnerMenuOpen] = useState(false);
  const [isAuhtorMenuOpen, setIsAuthorInnerMenuOpen] = useState(false);
  const [imgSrc, setImgSrc] = useState("/assets/all-images/profileAvater.png");
  const [isPublisherInnerMenuOpen, setIsPublisherInnerMenuOpen] =
    useState(false);
  const menuRef = useRef(null);
  const buttonRef = useRef(null);

  useEffect(() => {
    if (user?.profile_picture) {
      setImgSrc(user?.profile_picture);
    } else if (user?.profile_picture_url) {
      setImgSrc(user?.profile_picture_url);
    } else {
      setImgSrc("/assets/all-images/profileAvater.png");
    }
  }, [user]);

  useEffect(() => {
    if (!categories) dispatch(fetchCategories());
    if (!authors) dispatch(fetchAuthors());
    if (!publishers) dispatch(fetchPublishers());
  }, [dispatch, categories, authors, publishers]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    dispatch(logout());
  };

  return (
    <nav className="bg-white shadow-md">
      <div className="relative">
        <div className="w-full fixed z-40 bg-gray-100 left-0 py-2 hidden lg:block">
          <div className="flex items-center justify-between max-w-7xl mx-auto px-5 2xl:px-0 text-gray-500">
            <p>{t("welcome.to.yourepub")}</p>

            <div className="flex items-center gap-7">
              <p
                onClick={() => dispatch(setShowModal(true))}
                className="cursor-pointer hover:text-indigo-600"
              >
                <Icon
                  icon="material-symbols:sell-outline"
                  width="18"
                  height="18"
                  className="inline mb-0.5 mr-0.5"
                />{" "}
                {t("make.platform")}
              </p>
              <div className="h-3 w-px bg-gray-400" />
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_CONFIG_Redirect_URL}/editor/1/1`}
                target="_blank"
                className="hover:text-indigo-600"
              >
                <Icon
                  icon="solar:notebook-bookmark-linear"
                  width="18"
                  height="18"
                  className="inline mb-0.5 mr-2"
                />
                {t("try.ebook")}
              </Link>
              <div className="h-3 w-px bg-gray-400" />
              <Link
                href={`https://${
                  window.location.hostname === "yourepub.com"
                    ? "app.yourepub.com"
                    : "dev.yourepub.com"
                }/register`}
                target="_blank"
                className="hover:text-indigo-600"
              >
                <Icon
                  icon="gg:profile"
                  width="18"
                  height="18"
                  className="inline mb-0.5 mr-1"
                />
                {t("become.publisher")}
              </Link>
              {/* <div className="h-3 w-px bg-gray-400" />
              <p>
                <Icon
                  icon="mynaui:percentage-waves"
                  width="18"
                  height="18"
                  className="inline mb-0.5 mr-1"
                />
                {t("all.offers")}
              </p> */}
            </div>
          </div>
        </div>
        <div className="w-full fixed z-40 bg-white md:shadow left-0 lg:top-10">
          <div className="max-w-7xl mx-auto flex items-center justify-between px-4 xl:px-0 py-3">
            {/* Logo */}
            <div
              onClick={() => router.push("/")}
              className="flex items-center space-x-2 cursor-pointer"
            >
              <div className="relative w-32 md:w-40 h-10 max-sm:h-8">
                <Image
                  src="/assets/all-images/ebookLogo.png"
                  alt="Logo"
                  height={60}
                  width={200}
                  className="object-contain"
                  priority
                />
              </div>
            </div>

            {/* Search Bar */}
            <NavSearch isForSmallScreen={false} />

            {/* Icons & Buttons */}
            <div className="flex items-center space-x-3">
              {/* <Icon
                icon="famicons:notifications-outline"
                className="text-xl text-gray-500 max-sm:hidden"
              />
              <div className="h-3 w-px bg-gray-300 max-sm:hidden" />
              <button onClick={() => router.push("/cart-details")} className="text-gray-500 relative">
                <Icon
                  icon="ph:shopping-cart-simple-light"
                  className="text-xl text-gray-600"
                />
                {cart?.length > 0 && <span className="absolute bg-indigo-600 text-white p-1 h-4 w-4 rounded-full text-sm -right-2 -top-2 flex items-center justify-center">
                  {cart?.length > 9 ? "9+" : cart?.length}
                </span>}
              </button> */}
              <LanguageSwitcher />
              <div className="flex items-center text-gray-500 gap-2 max-sm:hidden">
                {/* <div className="h-3 w-px bg-gray-300" /> */}
                {isAuth ? (
                  <div className="relative group border rounded border-indigo-600 px-3 py-1.5">
                    <div className="flex items-center space-x-2">
                      <Image
                        src={imgSrc}
                        alt="User Avatar"
                        width={100}
                        height={100}
                        onError={() =>
                          setImgSrc("/assets/all-images/profileAvater.png")
                        }
                        className="rounded-full h-8 w-8 border border-indigo-500 object-contain"
                      />
                      <div className="flex flex-col">
                        {/* <span className="font-semibold text-xs text-gray-500">
                          {t("hello")},
                        </span> */}
                        <span className="text-gray-600 text-base font-semibold">
                          {user?.name?.split(" ")[0] || "User"}
                        </span>
                      </div>

                      <Icon
                        icon="oui:arrow-down"
                        width="14"
                        height="14"
                        className="group-hover:rotate-180 group-hover:text-indigo-500 transition-all duration-300"
                      />
                    </div>
                    <div className="absolute top-full pt-2 left-0 z-10 min-w-[180px] hidden group-hover:block">
                      <div className="bg-white shadow-lg rounded text-gray-600 p-2">
                        <button
                          onClick={() => (
                            router.push("/profile"),
                            dispatch(setMainProfileActiveTab(0))
                          )}
                          className="block px-3 py-2 hover:text-indigo-500 hover:bg-indigo-50 w-full rounded-lg text-start"
                        >
                          <Icon
                            icon="solar:user-linear"
                            width="20"
                            height="20"
                            className="inline mb-1 mr-1"
                          />{" "}
                          {t("dropdown.profile")}
                        </button>
                        <button
                          onClick={() => (
                            router.push("/profile"),
                            dispatch(setMainProfileActiveTab(1))
                          )}
                          className="block px-3 py-2 hover:text-indigo-500 hover:bg-indigo-50 w-full rounded-lg text-start"
                        >
                          <Icon
                            icon="fluent:box-16-regular"
                            width="18"
                            height="18"
                            className="inline mb-1 mr-1"
                          />{" "}
                          {t("profile.orders")}
                        </button>
                        <button
                          onClick={() => (
                            router.push("/profile"),
                            dispatch(setMainProfileActiveTab(3))
                          )}
                          className="block px-3 py-2 hover:text-indigo-500 hover:bg-indigo-50 w-full rounded-lg text-start"
                        >
                          <Icon
                            icon="solar:heart-linear"
                            width="18"
                            height="18"
                            className="inline mb-1 mr-1"
                          />{" "}
                          {t("profile.wishlist")}
                        </button>
                        <button
                          onClick={() => (
                            router.push("/profile"),
                            dispatch(
                              setMainProfileActiveTab(2)
                              // dispatch(setHistoryActiveCategory(2))
                            )
                          )}
                          className="block px-3 py-2 hover:text-indigo-500 hover:bg-indigo-50 w-full rounded-lg text-start"
                        >
                          <Icon
                            icon="material-symbols-light:menu-book-outline"
                            width="20"
                            height="20"
                            className="inline mb-1 mr-1"
                          />{" "}
                          {t("profile.ebook.library")}
                        </button>
                        <button
                          onClick={handleLogout}
                          className="block px-3 py-2 hover:bg-red-50 w-full hover:text-gray-700 text-start rounded-lg"
                        >
                          <Icon
                            icon="material-symbols-light:logout-rounded"
                            width="22"
                            height="22"
                            className="inline mb-1 mr-1"
                          />{" "}
                          {t("logout")}
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <>
                    <Link className="hover:text-indigo-500" href={"/login"}>
                      {t("login")}
                    </Link>
                    <div className="h-3 w-px bg-gray-300" />
                    <Link className="hover:text-indigo-500" href={"/register"}>
                      {t("register")}
                    </Link>
                  </>
                )}
              </div>

              <button
                onClick={() => (
                  router.push("/profile"),
                  dispatch(
                    setMainProfileActiveTab(2)
                    // dispatch(setHistoryActiveCategory(2))
                  )
                )}
                target="_blank"
                className="hidden sm:flex px-4 py-3 bg-gradient-to-r from-[#4136F1] to-[#8743FF] text-white rounded-md"
              >
                <Icon
                  icon="material-symbols-light:menu-book-outline"
                  width="20"
                  height="20"
                  className="inline mt-0.5 mr-1"
                />
                {t("elibrary.btn")}
              </button>
              {/* Mobile Menu Toggle */}
              <button
                ref={buttonRef}
                className="block sm:hidden text-gray-500"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                <Icon
                  icon={isMenuOpen ? "mdi:close" : "mdi:menu"}
                  className="text-2xl transition-all duration-300"
                />
              </button>
            </div>
          </div>
        </div>
        <div className="h-20 sm:h-32 md:h-[75px] lg:h-[113px]"></div>
      </div>

      {/* Navigation Links */}
      <div className="bg-[#4841BF] text-white">
        <div className="max-w-7xl mx-auto px-4 xl:px-0">
          <div className="hidden sm:flex flex-wrap sm:flex-nowrap items-center space-x-3 relative">
            <Link
              href={"/"}
              prefetch={true}
              className="py-3 hover:bg-white hover:text-[#4841BF] px-2 transition-all duration-200"
            >
              {t("home")}
            </Link>
            <div className="group cursor-pointer py-3 hover:bg-white hover:text-[#4841BF] px-2 transition-all duration-200">
              <p className="flex items-center gap-2">
                {t("category")}
                <Icon
                  icon="oui:arrow-down"
                  width="16"
                  height="16"
                  className="group-hover:rotate-180 transition-all duration-300"
                />
              </p>
              {/* Dropdown Menu */}
              <div
                className={`hidden group-hover:grid absolute top-full left-0 bg-white shadow-md z-20 p-4 rounded text-gray-600 
               grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 justify-center items-center min-w-full xl:min-w-[1280px] mx-auto`}
              >
                {categories?.length > 30 ? (
                  <>
                    {categories?.slice(0, 29).map((category, index) => (
                      <span key={index}>
                        <Link
                          href={"/category/" + category?.slug}
                          key={index}
                          className="mt-3 w-full hover:text-indigo-600 text-sm transition-all duration-200"
                        >
                          <Icon
                            icon="icon-park-outline:dot"
                            width="12"
                            height="12"
                            className="inline mb-0.5"
                          />{" "}
                          {category?.name}
                        </Link>
                      </span>
                    ))}
                    <span>
                      <Link
                        href={"/categories"}
                        className="mt-3 w-full hover:text-indigo-600 text-sm transition-all duration-200"
                      >
                        <Icon
                          icon="icon-park-outline:dot"
                          width="12"
                          height="12"
                          className="inline mb-0.5"
                        />{" "}
                        {t("others")}
                      </Link>
                    </span>
                  </>
                ) : (
                  categories?.data?.map((category, index) => (
                    <span key={index}>
                      <Link
                        href={"/category/" + category?.slug}
                        key={index}
                        className="mt-3 w-full hover:text-indigo-600 text-sm transition-all duration-200"
                      >
                        <Icon
                          icon="icon-park-outline:dot"
                          width="12"
                          height="12"
                          className="inline mb-0.5"
                        />{" "}
                        {category?.name}
                      </Link>
                    </span>
                  ))
                )}
              </div>
            </div>
            <div className="group cursor-pointer py-3 hover:bg-white hover:text-[#4841BF] px-2 transition-all duration-200">
              <p
                onClick={() => router.push("/authors")}
                className="flex items-center gap-2"
              >
                {t("writer")}
                <Icon
                  icon="oui:arrow-down"
                  width="16"
                  height="16"
                  className="group-hover:rotate-180 transition-all duration-300"
                />
              </p>
              {/* Dropdown Menu */}
              <div
                className={`hidden group-hover:grid absolute top-full left-0 bg-white shadow-md z-20 p-4 rounded text-gray-600 
               grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 justify-center items-center min-w-full xl:min-w-[1280px] mx-auto`}
              >
                {authors?.length > 30 ? (
                  <>
                    {authors?.slice(0, 29).map((author, index) => (
                      <span key={index}>
                        <Link
                          href={"/author/" + author?.slug}
                          key={index}
                          className="mt-3 w-full hover:text-indigo-600 text-sm transition-all duration-200"
                        >
                          <Icon
                            icon="icon-park-outline:dot"
                            width="12"
                            height="12"
                            className="inline mb-0.5"
                          />{" "}
                          {author?.name}
                        </Link>
                      </span>
                    ))}
                    <span>
                      <Link
                        href={"/authors"}
                        className="mt-3 w-full hover:text-indigo-600 text-sm transition-all duration-200"
                      >
                        <Icon
                          icon="icon-park-outline:dot"
                          width="12"
                          height="12"
                          className="inline mb-0.5"
                        />{" "}
                        {t("others")}
                      </Link>
                    </span>
                  </>
                ) : (
                  authors?.map((author, index) => (
                    <span key={index}>
                      <Link
                        href={"/author/" + author?.slug}
                        key={index}
                        className="mt-3 w-full hover:text-indigo-600 text-sm transition-all duration-200"
                      >
                        <Icon
                          icon="icon-park-outline:dot"
                          width="12"
                          height="12"
                          className="inline mb-0.5"
                        />{" "}
                        {author?.name}
                      </Link>
                    </span>
                  ))
                )}
              </div>
            </div>
            <div className="group cursor-pointer py-3 hover:bg-white hover:text-[#4841BF] px-2 transition-all duration-200">
              <p className="flex items-center gap-2">
                {t("publisher")}
                <Icon
                  icon="oui:arrow-down"
                  width="16"
                  height="16"
                  className="group-hover:rotate-180 transition-all duration-300"
                />
              </p>
              {/* Dropdown Menu */}
              <div
                className={`hidden group-hover:grid absolute top-full left-0 bg-white shadow-md z-20 p-4 rounded text-gray-600 
               grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 justify-center items-center min-w-full xl:min-w-[1280px] mx-auto`}
              >
                {publishers?.length > 30 ? (
                  <>
                    {publishers?.slice(0, 29).map((publisher, index) => (
                      <span key={index}>
                        <Link
                          href={"/publisher/" + publisher?.slug}
                          key={index}
                          className="mt-3 w-full hover:text-indigo-600 text-sm transition-all duration-200"
                        >
                          <Icon
                            icon="icon-park-outline:dot"
                            width="12"
                            height="12"
                            className="inline mb-0.5"
                          />{" "}
                          {publisher?.name}
                        </Link>
                      </span>
                    ))}
                    <span>
                      <Link
                        href={"/publishers"}
                        className="mt-3 w-full hover:text-indigo-600 text-sm transition-all duration-200"
                      >
                        <Icon
                          icon="icon-park-outline:dot"
                          width="12"
                          height="12"
                          className="inline mb-0.5"
                        />{" "}
                        {t("others")}
                      </Link>
                    </span>
                  </>
                ) : (
                  publishers?.map((publisher, index) => (
                    <span key={index}>
                      <Link
                        href={"/publisher/" + publisher?.slug}
                        key={index}
                        className="mt-3 w-full hover:text-indigo-600 text-sm transition-all duration-200"
                      >
                        <Icon
                          icon="icon-park-outline:dot"
                          width="12"
                          height="12"
                          className="inline mb-0.5"
                        />{" "}
                        {publisher?.name}
                      </Link>
                    </span>
                  ))
                )}
              </div>
            </div>
            {/* <p className="py-3 hover:bg-white hover:text-[#4841BF] px-2 transition-all duration-200">
              {t("flush")}
            </p>
            <p className="py-3 hover:bg-white hover:text-[#4841BF] px-2 transition-all duration-200">
              {t("collection")}
            </p>
            <p className="py-3 hover:bg-white hover:text-[#4841BF] px-2 transition-all duration-200">
              {t("offer")}
            </p> */}
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div
          ref={menuRef}
          className="sm:hidden bg-[#4841BF] text-white px-4 py-2 fixed top-14 w-full z-40"
        >
          <Link
            href={"/"}
            className="py-2 hover:bg-white hover:text-[#4841BF] px-2 transition-all duration-200 rounded"
          >
            {t("home")}
          </Link>
          <div
            className="cursor-pointer py-2 hover:bg-white hover:text-[#4841BF] group px-2 transition-all relative duration-200 rounded"
            onClick={() => (
              setIsCategoryInnerMenuOpen(!isCategoryInnerMenuOpen),
              setIsAuthorInnerMenuOpen(false),
              setIsPublisherInnerMenuOpen(false)
            )}
          >
            <p className="flex items-center gap-2">
              {t("category")}
              <Icon
                icon="oui:arrow-down"
                width="16"
                height="16"
                className={`transition-all duration-300 ${
                  isCategoryInnerMenuOpen ? "rotate-180" : ""
                }`}
              />
            </p>
            {isCategoryInnerMenuOpen && (
              <div className="absolute top-full left-0 bg-white shadow-md rounded-md p-2 max-h-60 overflow-y-auto text-gray-600 z-20 w-full mt-2 space-y-2">
                {categories?.data?.map((category, index) => (
                  <p key={index}>
                    <Link
                      href={"/category/" + category?.slug}
                      key={index}
                      className=" w-full"
                    >
                      {category?.name}
                    </Link>
                  </p>
                ))}
              </div>
            )}
          </div>
          <div
            className="cursor-pointer py-2 hover:bg-white hover:text-[#4841BF] group px-2 transition-all relative duration-200 rounded"
            onClick={() => (
              setIsCategoryInnerMenuOpen(false),
              setIsPublisherInnerMenuOpen(false),
              setIsAuthorInnerMenuOpen(!isAuhtorMenuOpen)
            )}
          >
            <p className="flex items-center gap-2">
              {t("writer")}
              <Icon
                icon="oui:arrow-down"
                width="16"
                height="16"
                className={`transition-all duration-300 ${
                  isAuhtorMenuOpen ? "rotate-180" : ""
                }`}
              />
            </p>
            {isAuhtorMenuOpen && (
              <div className="absolute top-full left-0 bg-white shadow-md rounded-md p-2 max-h-56 overflow-y-auto text-gray-600 z-20 w-full mt-2 space-y-2">
                {authors?.map((author, index) => (
                  <p key={index}>
                    <Link
                      href={"/author/" + author?.slug}
                      key={index}
                      className=" w-full"
                    >
                      {author?.name}
                    </Link>
                  </p>
                ))}
              </div>
            )}
          </div>
          <div
            className="cursor-pointer py-2 hover:bg-white hover:text-[#4841BF] group px-2 transition-all relative duration-200 rounded"
            onClick={() => (
              setIsCategoryInnerMenuOpen(false),
              setIsAuthorInnerMenuOpen(false),
              setIsPublisherInnerMenuOpen(!isPublisherInnerMenuOpen)
            )}
          >
            <p className="flex items-center gap-2">
              {t("publisher")}
              <Icon
                icon="oui:arrow-down"
                width="16"
                height="16"
                className={`transition-all duration-300 ${
                  isPublisherInnerMenuOpen ? "rotate-180" : ""
                }`}
              />
            </p>
            {isPublisherInnerMenuOpen && (
              <div className="absolute top-full left-0 bg-white shadow-md rounded-md p-2 max-h-52 overflow-y-auto text-gray-600 z-20 w-full mt-2 space-y-2">
                {publishers?.map((publisher, index) => (
                  <p key={index}>
                    <Link
                      href={"/publisher/" + publisher?.slug}
                      key={index}
                      className=" w-full"
                    >
                      {publisher?.name}
                    </Link>
                  </p>
                ))}
              </div>
            )}
          </div>
          <p className="py-2 hover:bg-white hover:text-[#4841BF] px-2 transition-all duration-200 rounded">
            {t("flush")}
          </p>
          <p className="py-2 hover:bg-white hover:text-[#4841BF] px-2 transition-all duration-200 rounded">
            {t("collection")}
          </p>
          <p className="py-2 hover:bg-white hover:text-[#4841BF] px-2 transition-all duration-200 rounded">
            {t("offer")}
          </p>

          <button className="flex items-center text-white gap-2 p-2 pt-0">
            <Icon
              icon="solar:user-linear"
              width="24"
              height="24"
              className="hidden"
            />
            {isAuth ? (
              <div className="relative group border bg-white rounded border-indigo-600 px-3 py-1.5">
                <div className="flex items-center space-x-2">
                  <Image
                    src={imgSrc}
                    alt="User Avatar"
                    width={20}
                    height={20}
                    onError={() =>
                      setImgSrc("/assets/all-images/profileAvater.png")
                    }
                    className="rounded-full h-8 w-8 border border-indigo-500"
                  />
                  <div className="flex flex-col">
                    <span className="font-semibold text-xs text-gray-500">
                      Hello,
                    </span>
                    <span className="text-gray-600 text-sm font-semibold">
                      {user?.name?.split(" ")[0] || "User"}
                    </span>
                  </div>

                  <Icon
                    icon="oui:arrow-down"
                    width="14"
                    height="14"
                    className="group-hover:rotate-180 group-hover:text-indigo-500 text-gray-500 transition-all duration-300"
                  />
                </div>
                <div className="absolute top-full pt-2 left-0 z-10 min-w-[180px] hidden group-hover:block">
                  <div className="bg-white shadow-lg rounded text-start text-gray-600 p-2">
                    <button
                      onClick={() => (
                        router.push("/profile"),
                        dispatch(setMainProfileActiveTab(0))
                      )}
                      className="block px-3 py-2 hover:text-indigo-500 hover:bg-indigo-50 w-full rounded-lg text-start"
                    >
                      <Icon
                        icon="solar:user-linear"
                        width="20"
                        height="20"
                        className="inline mb-1 mr-1"
                      />{" "}
                      {t("dropdown.profile")}
                    </button>
                    <button
                      onClick={() => (
                        router.push("/profile"),
                        dispatch(setMainProfileActiveTab(1))
                      )}
                      className="block px-3 py-2 hover:text-indigo-500 hover:bg-indigo-50 w-full rounded-lg text-start"
                    >
                      <Icon
                        icon="fluent:box-16-regular"
                        width="18"
                        height="18"
                        className="inline mb-1 mr-1"
                      />{" "}
                      {t("profile.orders")}
                    </button>
                    <button
                      onClick={() => (
                        router.push("/profile"),
                        dispatch(setMainProfileActiveTab(3))
                      )}
                      className="block px-3 py-2 hover:text-indigo-500 hover:bg-indigo-50 w-full rounded-lg text-start"
                    >
                      <Icon
                        icon="solar:heart-linear"
                        width="18"
                        height="18"
                        className="inline mb-1 mr-1"
                      />{" "}
                      {t("profile.wishlist")}
                    </button>
                    <button
                      onClick={() => (
                        router.push("/profile"),
                        dispatch(
                          setMainProfileActiveTab(2)
                          // dispatch(setHistoryActiveCategory(2))
                        )
                      )}
                      className="block px-3 py-2 hover:text-indigo-500 hover:bg-indigo-50 w-full rounded-lg text-start"
                    >
                      <Icon
                        icon="material-symbols-light:menu-book-outline"
                        width="20"
                        height="20"
                        className="inline mb-1 mr-1"
                      />{" "}
                      {t("profile.ebook.library")}
                    </button>
                    <button
                      onClick={handleLogout}
                      className="block px-3 py-2 hover:bg-red-50 w-full hover:text-gray-700 text-start rounded-lg text-start"
                    >
                      <Icon
                        icon="material-symbols-light:logout-rounded"
                        width="22"
                        height="22"
                        className="inline mb-1 mr-1"
                      />{" "}
                      {t("logout")}
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <>
                <Link className="hover:text-indigo-500" href={"/login"}>
                  {t("login")}{" "}
                </Link>
                <Link className="hover:text-indigo-500" href={"/register"}>
                  / {t("register")}
                </Link>
              </>
            )}
          </button>
          <button className="flex px-4 py-3 bg-gradient-to-r from-[#4136F1] to-[#8743FF] text-white rounded-md">
            {t("try.ebook")}
          </button>
        </div>
      )}

      <div>
        <div className="px-5 py-2 fixed top-[50px] sm:top-[70px] left-0 z-20 w-full bg-white md:hidden">
          <NavSearch isForSmallScreen={true} />
        </div>
        <div className="max-sm:h-[20px] sm:hidden"></div>
      </div>
    </nav>
  );
};

export default Navbar;
