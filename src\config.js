// API and service URLs
export const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:8000';
export const API_URL = `${BASE_URL}/api`;
export const BASE_CONFIG_URL = process.env.NEXT_PUBLIC_BASE_CONFIG_URL

// Authentication
export const COOKIE_DOMAIN = process.env.NEXT_PUBLIC_COOKIE_URL || '.yourepub.com';
export const GOOGLE_CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
export const COOKIE_OPTIONS = {
  expires: 7, // 7 days
  path: '/',
  ...(COOKIE_DOMAIN && { domain: COOKIE_DOMAIN }),
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax'
};

// Application settings
export const APP_ENV = process.env.NODE_ENV; 
export const IS_PROD = process.env.NODE_ENV === 'production';
export const IS_DEV = process.env.NODE_ENV === 'development';